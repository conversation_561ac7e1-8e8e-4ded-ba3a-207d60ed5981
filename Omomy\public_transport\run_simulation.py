#!/usr/bin/env python
"""
Simple script to run driver movement simulation
Usage: python run_simulation.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'public_transport.settings')
django.setup()

from django.core.management import call_command

def main():
    print("🚗 Starting Driver Movement Simulation")
    print("=" * 50)
    print("This will simulate driver movement for 5 minutes")
    print("Each driver will move every 10 seconds")
    print("You can watch the changes in the public view at: http://localhost:8000/drivers/public/")
    print("=" * 50)
    
    try:
        # Run simulation for 5 minutes (300 seconds) with 10-second intervals
        call_command('simulate_driver_movement', 
                    duration=300, 
                    interval=10, 
                    create_test_drivers=True)
    except KeyboardInterrupt:
        print("\n🛑 Simulation stopped by user")
    except Exception as e:
        print(f"❌ Error running simulation: {e}")

if __name__ == "__main__":
    main()
